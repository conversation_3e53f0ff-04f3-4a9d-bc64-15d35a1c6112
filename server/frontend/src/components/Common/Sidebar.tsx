import { useQueryClient } from "@tanstack/react-query"
import { useState } from "react"
import { FaBars } from "react-icons/fa"
import { FiLogOut } from "react-icons/fi"

import type { UserPublic } from "@/client"
import useAuth from "@/hooks/useAuth"
import { Button } from "../ui/button"
import {
  Drawer,
  DrawerContent,
  DrawerTrigger,
} from "../ui/drawer"
import SidebarItems from "./SidebarItems"

const Sidebar = () => {
  const queryClient = useQueryClient()
  const currentUser = queryClient.getQueryData<UserPublic>(["currentUser"])
  const { logout } = useAuth()
  const [open, setOpen] = useState(false)

  const handleLogout = async () => {
    logout()
  }

  return (
    <>
      {/* Mobile */}
      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="flex md:hidden absolute z-[100] m-4"
            aria-label="Open Menu"
          >
            <FaBars />
          </Button>
        </DrawerTrigger>
        <DrawerContent className="max-w-[280px]">
          <div className="flex flex-col justify-between p-4">
            <div>
              <SidebarItems onClose={() => setOpen(false)} />
              <button
                onClick={handleLogout}
                className="flex items-center gap-4 px-4 py-2 w-full text-left hover:bg-muted rounded"
              >
                <FiLogOut />
                <span>Log Out</span>
              </button>
            </div>
            {currentUser?.email && (
              <p className="text-sm p-2">
                Logged in as: {currentUser.email}
              </p>
            )}
          </div>
        </DrawerContent>
      </Drawer>

      {/* Desktop */}
      <div className="hidden md:flex sticky top-0 bg-muted min-w-[280px] h-screen p-4">
        <div className="w-full">
          <SidebarItems />
        </div>
      </div>
    </>
  )
}

export default Sidebar
