"use client"

import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Phone,
  PhoneOff,
  Mic,
  MicOff,
  Volume2,
  VolumeX,
  RepeatIcon as Record,
  Square,
  Globe,
  MessageSquare,
  ChevronRight,
  ChevronLeft,
  Send,
  Bot,
  FileText,
} from "lucide-react"

export default function TranslationInterface() {
  const [isCallActive, setIsCallActive] = useState(true)
  const [isMuted, setIsMuted] = useState(false)
  const [isRecording, setIsRecording] = useState(true)
  const [speakerOn, setSpeakerOn] = useState(true)
  const [callDuration, setCallDuration] = useState(0)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [chatMessage, setChatMessage] = useState("")
  const [activeTab, setActiveTab] = useState("translation")
  const [highlightedMessageId, setHighlightedMessageId] = useState<string | null>(null)

  // Simulate call duration timer
  useEffect(() => {
    if (isCallActive) {
      const timer = setInterval(() => {
        setCallDuration((prev) => prev + 1)
      }, 1000)
      return () => clearInterval(timer)
    }
  }, [isCallActive])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  const mockChatMessages = [
    {
      id: "event-1",
      type: "event",
      eventType: "location_detected",
      message: "Location detected: 123 Main Street",
      timestamp: "14:32:22",
      confidence: 0.98,
    },
    {
      id: "ai-1",
      type: "ai",
      message: "I'm analyzing the emergency call. The caller needs medical assistance at 123 Main Street.",
      timestamp: "14:32:30",
      sources: [
        { id: "transcript-0", text: "I need urgent medical help", speaker: "Caller", timestamp: "14:32:15" },
        { id: "transcript-2", text: "I'm at 123 Main Street", speaker: "Caller", timestamp: "14:32:22" },
      ],
      confidence: 0.95,
    },
    {
      id: "event-2",
      type: "event",
      eventType: "emergency_type_identified",
      message: "Emergency type identified: Medical Emergency",
      timestamp: "14:32:32",
      confidence: 0.89,
    },
    {
      id: "user-1",
      type: "user",
      message: "What type of medical emergency?",
      timestamp: "14:32:35",
    },
    {
      id: "ai-2",
      type: "ai",
      message:
        "Based on the conversation, it appears to be a general medical emergency. The caller hasn't specified symptoms yet. I'm monitoring for additional details about the nature of the emergency.",
      timestamp: "14:32:40",
      sources: [{ id: "transcript-0", text: "I need urgent medical help", speaker: "Caller", timestamp: "14:32:15" }],
      confidence: 0.78,
    },
    {
      id: "event-3",
      type: "event",
      eventType: "language_detected",
      message: "Language detected: Spanish → English translation active",
      timestamp: "14:32:45",
      confidence: 0.96,
    },
    {
      id: "event-4",
      type: "event",
      eventType: "services_dispatched",
      message: "Emergency services dispatched to confirmed location. Language barrier resolved.",
      timestamp: "14:33:10",
      sources: [
        { id: "transcript-3", text: "Help is on the way", speaker: "Operator", timestamp: "14:32:25" },
        { id: "transcript-2", text: "I'm at 123 Main Street", speaker: "Caller", timestamp: "14:32:22" },
      ],
      confidence: 0.92,
    },
  ]

  const mockTranscript = [
    {
      id: "transcript-0",
      speaker: "Caller",
      text: "I need urgent medical help",
      original: "Necesito ayuda médica urgente",
      timestamp: "14:32:15",
    },
    {
      id: "transcript-1",
      speaker: "Operator",
      text: "What is your location?",
      original: "¿Cuál es su ubicación?",
      timestamp: "14:32:18",
    },
    {
      id: "transcript-2",
      speaker: "Caller",
      text: "I'm at 123 Main Street",
      original: "Estoy en el 123 Main Street",
      timestamp: "14:32:22",
    },
    {
      id: "transcript-3",
      speaker: "Operator",
      text: "Help is on the way",
      original: "La ayuda está en camino",
      timestamp: "14:32:25",
    },
  ]

  const emergencySummary = {
    location: "123 Main Street, New York, NY",
    emergencyType: "Medical Emergency",
    caller: "Spanish-speaking individual",
    timeReported: "14:32:15",
    status: "Help dispatched",
    keyDetails: [
      "Caller requested urgent medical help",
      "Location confirmed as 123 Main Street",
      "Emergency services notified",
      "Language barrier resolved through translation",
    ],
  }

  const timelineEvents = [
    {
      id: "timeline-1",
      time: "14:32:15",
      type: "call_initiated",
      title: "Emergency Call Received",
      description: "Caller requested urgent medical help",
      icon: "📞",
      status: "completed",
    },
    {
      id: "timeline-2",
      time: "14:32:22",
      type: "location_confirmed",
      title: "Location Confirmed",
      description: "Address verified as 123 Main Street, New York, NY",
      icon: "📍",
      status: "completed",
    },
    {
      id: "timeline-3",
      time: "14:32:25",
      type: "services_notified",
      title: "Emergency Services Notified",
      description: "Medical emergency dispatch initiated",
      icon: "🚑",
      status: "completed",
    },
    {
      id: "timeline-4",
      time: "14:32:45",
      type: "translation_active",
      title: "Translation System Active",
      description: "Spanish to English translation established",
      icon: "🌐",
      status: "completed",
    },
    {
      id: "timeline-5",
      time: "14:33:10",
      type: "help_dispatched",
      title: "Help Dispatched",
      description: "Emergency responders en route to location",
      icon: "🚨",
      status: "in_progress",
    },
  ]

  const handleSendMessage = () => {
    if (chatMessage.trim()) {
      setChatMessage("")
      // Handle sending message to LLM
    }
  }

  const handleSourceClick = (sourceId: string) => {
    setHighlightedMessageId(sourceId)
    setActiveTab("translation")

    // Auto-hide highlight after 3 seconds
    setTimeout(() => {
      setHighlightedMessageId(null)
    }, 3000)
  }

  const EventIcon = ({ eventType }: { eventType: string }) => {
    const getEventIcon = (type: string) => {
      switch (type) {
        case "location_detected":
          return "📍"
        case "emergency_type_identified":
          return "🚨"
        case "language_detected":
          return "🌐"
        case "caller_info_updated":
          return "👤"
        case "priority_changed":
          return "⚠️"
        case "services_dispatched":
          return "🚑"
        default:
          return "ℹ️"
      }
    }

    return <span className="text-sm">{getEventIcon(eventType)}</span>
  }

  const ConfidenceIndicator = ({ confidence }: { confidence: number }) => {
    const getConfidenceColor = (conf: number) => {
      if (conf >= 0.9) return "bg-green-500"
      if (conf >= 0.7) return "bg-yellow-500"
      return "bg-red-500"
    }

    const getConfidenceText = (conf: number) => {
      if (conf >= 0.9) return "High"
      if (conf >= 0.7) return "Medium"
      return "Low"
    }

    return (
      <div className="flex items-center gap-1">
        <div className={`h-2 w-2 rounded-full ${getConfidenceColor(confidence)}`}></div>
        <span className="text-xs text-muted-foreground">
          {getConfidenceText(confidence)} ({Math.round(confidence * 100)}%)
        </span>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <div className="flex-1 p-4">
        <div className="mx-auto max-w-6xl space-y-4">
          {/* Header with Tabs */}
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold">Real-Time Translation Interface</h1>
            <div className="flex items-center gap-4">
              <Badge variant={isCallActive ? "default" : "secondary"} className="text-sm">
                {isCallActive ? "CALL ACTIVE" : "CALL ENDED"}
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="flex items-center gap-2"
              >
                <MessageSquare className="h-4 w-4" />
                AI Assistant
                {sidebarOpen ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
              </Button>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="translation" className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Live Translation
              </TabsTrigger>
              <TabsTrigger value="summary" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                AI Summary
              </TabsTrigger>
            </TabsList>

            <TabsContent value="translation" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                {/* Translation Display - now takes 2 columns */}
                <Card className="lg:col-span-2">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <Globe className="h-5 w-5" />
                        Translation Feed
                      </CardTitle>
                      <div className="flex items-center gap-2">
                        <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span className="text-sm text-muted-foreground">Live</span>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-96">
                      <div className="space-y-4">
                        {mockTranscript.map((entry, index) => (
                          <div
                            key={index}
                            className={`space-y-2 transition-all duration-500 ${
                              highlightedMessageId === entry.id
                                ? "bg-blue-50 border-blue-200 border-2 rounded-lg p-2"
                                : ""
                            }`}
                          >
                            <div className="flex items-center gap-2">
                              <Badge variant={entry.speaker === "Caller" ? "secondary" : "default"} className="text-xs">
                                {entry.speaker}
                              </Badge>
                              <span className="text-xs text-muted-foreground">{entry.timestamp}</span>
                              {highlightedMessageId === entry.id && (
                                <Badge variant="outline" className="text-xs bg-blue-100">
                                  Referenced by AI
                                </Badge>
                              )}
                            </div>

                            <div className="p-3 bg-white rounded-lg border">
                              <p className="text-sm font-medium mb-1">{entry.text}</p>
                              <p className="text-xs text-muted-foreground italic">{entry.original}</p>
                            </div>

                            {index < mockTranscript.length - 1 && <Separator className="my-2" />}
                          </div>
                        ))}
                      </div>
                    </ScrollArea>

                    {/* Current Input */}
                    <div className="mt-4 p-3 border-2 border-dashed border-gray-300 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="h-2 w-2 bg-red-500 rounded-full animate-pulse"></div>
                        <span className="text-sm font-medium">Listening...</span>
                      </div>
                      <p className="text-sm text-muted-foreground italic">Waiting for speech input...</p>
                    </div>
                  </CardContent>
                </Card>

                {/* Call Information Panel - now on the right */}
                <Card className="lg:col-span-1">
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center gap-2 text-lg">
                      <Phone className="h-4 w-4" />
                      Call Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Caller:</span>
                        <span>+****************</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Location:</span>
                        <span>New York, NY</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Duration:</span>
                        <span>{formatTime(callDuration)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Priority:</span>
                        <Badge variant="destructive" className="text-xs">
                          EMERGENCY
                        </Badge>
                      </div>
                    </div>

                    <Separator />

                    {/* Call Controls with text labels */}
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">Call Controls</h4>
                      <div className="space-y-2">
                        <Button
                          variant={isMuted ? "destructive" : "outline"}
                          size="sm"
                          onClick={() => setIsMuted(!isMuted)}
                          className="w-full flex items-center gap-2 justify-start"
                        >
                          {isMuted ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                          {isMuted ? "Unmute Microphone" : "Mute Microphone"}
                        </Button>

                        <Button
                          variant={speakerOn ? "default" : "outline"}
                          size="sm"
                          onClick={() => setSpeakerOn(!speakerOn)}
                          className="w-full flex items-center gap-2 justify-start"
                        >
                          {speakerOn ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
                          {speakerOn ? "Turn Off Speaker" : "Turn On Speaker"}
                        </Button>

                        <Button
                          variant={isRecording ? "destructive" : "outline"}
                          size="sm"
                          onClick={() => setIsRecording(!isRecording)}
                          className="w-full flex items-center gap-2 justify-start"
                        >
                          {isRecording ? <Square className="h-4 w-4" /> : <Record className="h-4 w-4" />}
                          {isRecording ? "Stop Recording" : "Start Recording"}
                        </Button>

                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => setIsCallActive(false)}
                          className="w-full flex items-center gap-2 justify-start"
                        >
                          <PhoneOff className="h-4 w-4" />
                          End Call
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="summary" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {/* Emergency Summary Card */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Bot className="h-5 w-5" />
                      Emergency Summary
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 gap-4">
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground">LOCATION</h4>
                          <p className="text-sm">{emergencySummary.location}</p>
                        </div>
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground">EMERGENCY TYPE</h4>
                          <p className="text-sm">{emergencySummary.emergencyType}</p>
                        </div>
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground">CALLER</h4>
                          <p className="text-sm">{emergencySummary.caller}</p>
                        </div>
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground">TIME REPORTED</h4>
                          <p className="text-sm">{emergencySummary.timeReported}</p>
                        </div>
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground">STATUS</h4>
                          <Badge variant="default">{emergencySummary.status}</Badge>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground mb-2">KEY DETAILS</h4>
                      <ul className="space-y-1">
                        {emergencySummary.keyDetails.map((detail, index) => (
                          <li key={index} className="text-sm flex items-start gap-2">
                            <span className="text-muted-foreground">•</span>
                            {detail}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>

                {/* Timeline Card */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Event Timeline
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-96">
                      <div className="space-y-4">
                        {timelineEvents.map((event, index) => (
                          <div key={event.id} className="flex gap-3">
                            <div className="flex flex-col items-center">
                              <div
                                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm ${
                                  event.status === "completed"
                                    ? "bg-green-100"
                                    : event.status === "in_progress"
                                      ? "bg-blue-100"
                                      : "bg-gray-100"
                                }`}
                              >
                                {event.icon}
                              </div>
                              {index < timelineEvents.length - 1 && <div className="w-0.5 h-8 bg-gray-200 mt-2"></div>}
                            </div>
                            <div className="flex-1 pb-4">
                              <div className="flex items-center justify-between mb-1">
                                <h4 className="text-sm font-medium">{event.title}</h4>
                                <span className="text-xs text-muted-foreground">{event.time}</span>
                              </div>
                              <p className="text-xs text-muted-foreground">{event.description}</p>
                              <Badge
                                variant={event.status === "completed" ? "default" : "secondary"}
                                className="text-xs mt-1"
                              >
                                {event.status === "completed"
                                  ? "Completed"
                                  : event.status === "in_progress"
                                    ? "In Progress"
                                    : "Pending"}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>

          {/* Status Bar */}
          <Card>
            <CardContent className="py-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm">Translation: Online</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm">Audio: Excellent</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 bg-yellow-500 rounded-full"></div>
                    <span className="text-sm">Latency: 150ms</span>
                  </div>
                </div>

                <div className="text-sm text-muted-foreground">OP-2024-001 | TR-{Date.now().toString().slice(-6)}</div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Expandable Sidebar */}
      {sidebarOpen && (
        <div className="w-80 bg-white border-l border-gray-200 flex flex-col max-h-screen">
          <div className="p-4 border-b flex-shrink-0">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold flex items-center gap-2">
                <Bot className="h-4 w-4" />
                AI Assistant
              </h3>
              <Button variant="ghost" size="sm" onClick={() => setSidebarOpen(false)}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="flex-1 min-h-0 p-4">
            <ScrollArea className="h-full">
              <div className="space-y-4 pr-4">
                {mockChatMessages.map((msg, index) => (
                  <div key={index}>
                    {msg.type === "event" ? (
                      // AI Event Notification
                      <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
                        <div className="flex items-start gap-2">
                          <EventIcon eventType={msg.eventType} />
                          <div className="flex-1">
                            <div className="flex items-center justify-between mb-1">
                              <Badge variant="outline" className="text-xs bg-amber-100 text-amber-800 border-amber-300">
                                AI Event
                              </Badge>
                              <span className="text-xs text-amber-600">{msg.timestamp}</span>
                            </div>
                            <p className="text-sm text-amber-900 font-medium">{msg.message}</p>
                            {msg.confidence && (
                              <div className="mt-2">
                                <ConfidenceIndicator confidence={msg.confidence} />
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ) : (
                      // Regular Chat Messages
                      <div className={`flex ${msg.type === "user" ? "justify-end" : "justify-start"}`}>
                        <div className="max-w-[90%] space-y-2">
                          <div
                            className={`p-3 rounded-lg text-sm ${
                              msg.type === "user" ? "bg-blue-500 text-white" : "bg-gray-100 text-gray-900"
                            }`}
                          >
                            <p>{msg.message}</p>
                            <div
                              className={`flex items-center justify-between mt-2 ${
                                msg.type === "user" ? "text-blue-100" : "text-gray-500"
                              }`}
                            >
                              <p className="text-xs">{msg.timestamp}</p>
                              {msg.confidence && <ConfidenceIndicator confidence={msg.confidence} />}
                            </div>
                          </div>

                          {/* Source Citations */}
                          {msg.sources && msg.sources.length > 0 && (
                            <div className="bg-gray-50 rounded-lg p-2 border">
                              <div className="flex items-center gap-1 mb-2">
                                <FileText className="h-3 w-3 text-gray-500" />
                                <span className="text-xs font-medium text-gray-600">
                                  Sources ({msg.sources.length})
                                </span>
                              </div>
                              <div className="space-y-1">
                                {msg.sources.map((source, sourceIndex) => (
                                  <button
                                    key={sourceIndex}
                                    onClick={() => handleSourceClick(source.id)}
                                    className="w-full text-left p-2 rounded bg-white hover:bg-blue-50 border border-gray-200 hover:border-blue-300 transition-colors"
                                  >
                                    <div className="flex items-center justify-between mb-1">
                                      <Badge
                                        variant={source.speaker === "Caller" ? "secondary" : "default"}
                                        className="text-xs"
                                      >
                                        {source.speaker}
                                      </Badge>
                                      <span className="text-xs text-gray-500">{source.timestamp}</span>
                                    </div>
                                    <p className="text-xs text-gray-700 truncate">"{source.text}"</p>
                                  </button>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>

          <div className="p-4 border-t flex-shrink-0">
            <div className="flex gap-2">
              <Input
                placeholder="Ask AI about the call..."
                value={chatMessage}
                onChange={(e) => setChatMessage(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
                className="flex-1"
              />
              <Button size="sm" onClick={handleSendMessage}>
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
