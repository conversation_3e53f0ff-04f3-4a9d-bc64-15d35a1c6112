import { Badge } from "@/components/ui/badge"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Phone } from "lucide-react"
import { CallControls } from "./CallControls"

interface CallInformationProps {
  callDuration: number
  isMuted: boolean
  setIsMuted: (muted: boolean) => void
  speakerOn: boolean
  setSpeakerOn: (on: boolean) => void
  isRecording: boolean
  setIsRecording: (recording: boolean) => void
  isCallActive: boolean
  setIsCallActive: (active: boolean) => void
}

export function CallInformation({
  callDuration,
  isMuted,
  setIsMuted,
  speakerOn,
  setSpeakerOn,
  isRecording,
  setIsRecording,
  isCallActive,
  setIsCallActive,
}: CallInformationProps) {
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  return (
    <Card className="lg:col-span-1">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Phone className="h-4 w-4" />
          Call Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3 text-sm">
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Caller:</span>
            <span>+****************</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Location:</span>
            <span>New York, NY</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Duration:</span>
            <span>{formatTime(callDuration)}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Priority:</span>
            <Badge variant="destructive" className="text-xs">
              EMERGENCY
            </Badge>
          </div>
        </div>

        <Separator />

        <CallControls
          isMuted={isMuted}
          setIsMuted={setIsMuted}
          speakerOn={speakerOn}
          setSpeakerOn={setSpeakerOn}
          isRecording={isRecording}
          setIsRecording={setIsRecording}
          isCallActive={isCallActive}
          setIsCallActive={setIsCallActive}
        />
      </CardContent>
    </Card>
  )
}
